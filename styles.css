/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Remove underlines from select elements and form controls */
select,
input,
textarea,
button,
.variant-option,
.address-card,
.payment-method {
    text-decoration: none !important;
}

/* Darker borders for all form elements */
select,
input[type="text"],
input[type="email"],
input[type="tel"],
input[type="password"],
input[type="number"],
textarea {
    border: 2px solid #333 !important;
}

select:focus,
input:focus,
textarea:focus {
    border-color: #000 !important;
    text-decoration: none !important;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: #000;
    background: #fff;
    overflow-x: hidden;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 30px;
}

/* Premium Typography */
.serif {
    font-family: 'Playfair Display', serif;
}

/* Header Styles */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid #e5e5e5;
    position: sticky;
    top: 0;
    z-index: 1000;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.header.scrolled {
    background: rgba(238, 236, 236, 0.95);
    color: white;
    border-bottom: 1px solid #333;
}

.header.scrolled .logo,
.header.scrolled .header-icon,
.header.scrolled .nav-links a {
    color: rgb(0, 0, 0);
}

.header.scrolled .search-bar {
    background: rgba(0, 0, 0, 0);
    border-color: rgb(0, 0, 0);
    color: rgb(0, 0, 0);
}

.header.scrolled .search-bar::placeholder {
    color: rgba(0, 0, 0, 0.7);
}

.navbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem 0;
    flex-wrap: wrap;
    position: relative;
}

.logo {
    margin-right: auto;
    display: flex;
    align-items: center;
    font-family: 'Playfair Display', serif;
    font-size: 2.2rem;
    font-weight: 800;
    color: #000;
    text-decoration: none;
    letter-spacing: -0.02em;
    transition: all 0.3s ease;
}

.logo:hover {
    transform: scale(1.05);
}

.search-container {
    flex: 1;
    max-width: 500px;
    margin: 0 3rem;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
}

.search-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
    background: #f5f5f5;
    border-radius: 8px;
}

.search-icon-container {
    position: absolute;
    left: 12px;
    display: flex;
    align-items: center;
    pointer-events: none;
}

.search-svg {
    width: 20px;
    height: 20px;
    color: #666;
}

.search-bar {
    width: 600px;
    padding: 12px 12px 12px 40px;
    border: none;
    border-radius: 8px;
    outline: none;
    font-size: 14px;
    background: transparent;
}

.search-bar:focus {
    box-shadow: 0 0 0 2px rgba(17, 17, 17, 0.2);
}

.search-button {
    margin-left: 8px;
    padding: 8px 16px;
    background-color: #101111;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
}

.search-button:hover {
    background-color: #3182ce;
}

/* Search Suggestions Dropdown */
.search-suggestions-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    z-index: 1000;
    max-height: 400px;
    overflow-y: auto;
    margin-top: 4px;
}

.suggestion-item {
    padding: 12px 16px;
    border-bottom: 1px solid #f5f5f5;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 12px;
}

.suggestion-item:last-child {
    border-bottom: none;
}

.suggestion-item:hover,
.suggestion-item.highlighted {
    background: #f8f9fa;
    transform: translateX(2px);
}

.suggestion-icon {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    font-size: 12px;
    flex-shrink: 0;
}

.suggestion-icon.product {
    background: #e3f2fd;
    color: #1976d2;
}

.suggestion-icon.category {
    background: #f3e5f5;
    color: #7b1fa2;
}

.suggestion-icon.search {
    background: #e8f5e8;
    color: #388e3c;
}

.suggestion-content {
    flex: 1;
    min-width: 0;
}

.suggestion-title {
    font-weight: 500;
    color: #333;
    font-size: 14px;
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.suggestion-subtitle {
    font-size: 12px;
    color: #666;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.suggestion-loading {
    padding: 16px;
    text-align: center;
    color: #666;
    font-size: 14px;
}

.suggestion-no-results {
    padding: 16px;
    text-align: center;
    color: #999;
    font-size: 14px;
}

.search-wrapper {
    position: relative;
}

.header-icons {
    display: flex;
    gap: 2rem;
    align-items: center;
}

.header-icon {
    font-size: 1.4rem;
    color: #000;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.header-icon:hover {
    transform: translateY(-2px) scale(1.1);
}

.cart-count {
    position: absolute;
    top: -10px;
    right: -10px;
    background: #000;
    color: white;
    border-radius: 50%;
    width: 22px;
    height: 22px;
    font-size: 0.75rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    justify-content: center;
}

.nav-menu {
    width: 100%;
    margin-top: 1.5rem;
}

.nav-links {
    display: flex;
    list-style: none;
    gap: 3rem;
    flex-wrap: wrap;
    justify-content: center;
    padding: 20px;
}

.nav-links li {
    display: flex;
    align-items: center;
}

.nav-links a {
    color: #333;
    text-decoration: none;
    font-size: 0.85rem;
    font-weight: 500;
    letter-spacing: 0.5px;
    transition: color 0.3s ease;
    white-space: nowrap;
}

.nav-links a:hover {
    color: #000;
}

@media (max-width: 1200px) {
    .nav-links {
        gap: 2rem;
    }
    
    .nav-links a {
        font-size: 0.8rem;
    }
}

@media (max-width: 992px) {
    .nav-links {
        gap: 1.5rem;
        justify-content: flex-start;
        overflow-x: auto;
        padding: 0.5rem;
        -webkit-overflow-scrolling: touch;
    }
}

/* HERO SECTION REWORK */
.hero {
    background: url('assets/Hero-Image.jpg') center center/cover no-repeat;
    color: #111;
    padding: 0;
    position: relative;
    min-height: 650px;
    height: 65vh;
    max-height: 900px;
    box-shadow: 0 2px 24px rgba(0,0,0,0.03);
    display: flex;
    align-items: stretch;
    justify-content: center;
}
.hero::before {
    content: '';
    position: absolute;
    top: 0; left: 0; right: 0; bottom: 0;
    background: linear-gradient(90deg, rgba(255,255,255,0.02) 0%, rgba(0,0,0,0.10) 100%);
    z-index: 1;
}
.hero-overlay-content {
    position: relative;
    z-index: 2;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-items: flex-start;
    min-height: 650px;
}
.hero-text {
    max-width: 600px;
    margin-left: auto;
    margin-right: 4vw;
    margin-top: 12rem;
    margin-bottom: 2rem;
    align-self: flex-start;
}
.hero-badge {
    position: absolute;
    bottom: 32px;
    right: 0px;
    background: #fff;
    color: #222;
    font-weight: 700;
    font-size: 1rem;
    border-radius: 10px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 0.7rem 1.3rem;
    text-align: center;
    line-height: 1.2;
    border: 1px solid #eee;
    z-index: 3;
}
.hero-subtitle {
    font-size: 1.2rem;
    font-weight: 600;
    color: #444;
    text-transform: uppercase;
    letter-spacing: 2px;
    margin-bottom: 0rem;
    
    display: inline-block;
    padding: 0rem 0rem;

}
.hero-buttons .btn {
    display: inline-block;
    padding: 1rem 2rem;
    font-size: 1rem;
    font-weight: 600;
    text-decoration: none;
    border-radius: 50px;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}
.hero-buttons .btn-primary {
    background: #000;
    color: #fff;
    border: 2px solid #000;
}
.hero-buttons .btn-primary:hover {
    background: #fff;
    color: #000;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}
.hero-buttons .btn-secondary {
    background: transparent;
    color: #000;
    border: 2px solid #000;
}
.hero-buttons .btn-secondary:hover {
    background: #000;
    color: #fff;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}
@media (max-width: 900px) {
    .hero {
        min-height: 420px;
        height: 45vh;
    }
    .hero-overlay-content {
        min-height: 420px;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }
    .hero-text {
        margin-left: 0.5rem;
        margin-right: 0.5rem;
        margin-top: 2rem;
    }
    .hero-badge {
        right: 40px;
        bottom: 16px;
        font-size: 0.95rem;
        padding: 0.6rem 1rem;
    }
}
@media (max-width: 600px) {
    .hero {
        min-height: 320px;
        height: 32vh;
    }
    .hero-overlay-content {
        min-height: 320px;
    }
    .hero-text {
        margin-top: 1.2rem;
        margin-bottom: 1.2rem;
        max-width: 98vw;
        margin-right: 0.5rem;
    }
    .hero-badge {
        right: 16px;
        bottom: 8px;
        font-size: 0.9rem;
        padding: 0.5rem 0.7rem;
    }
}

/* Vitamin Categories */
.vitamin-categories {
    padding: 8rem 0;
    background: #f8f8f8;
}

.section-title {
    text-align: center;
    font-family: 'Playfair Display', serif;
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 4rem;
    color: #000;
    letter-spacing: -0.02em;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: #000;
}

.category-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 3rem;
}

.category-card {
    background: white;
    padding: 3rem;
    text-align: center;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    border: 1px solid #e5e5e5;
    position: relative;
    overflow: hidden;
}

.category-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0,0,0,0.05), transparent);
    transition: left 0.5s;
}

.category-card:hover::before {
    left: 100%;
}

.category-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 30px 80px rgba(0, 0, 0, 0.15);
}

.category-card.men { border-top: 4px solid #000; }
.category-card.women { border-top: 4px solid #666; }
.category-card.kids { border-top: 4px solid #999; }
.category-card.more { border-top: 4px solid #333; }

.category-icon {
    font-size: 4rem;
    margin-bottom: 2rem;
    color: #000;
}

.category-card h3 {
    font-family: 'Playfair Display', serif;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #000;
}

.category-card p {
    font-size: 1.1rem;
    color: #666;
    line-height: 1.6;
}



.product-card {
    background: #fff;
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(40,116,240,0.10), 0 1.5px 8px rgba(0,0,0,0.06);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    position: relative;
    padding: 0;
    min-width: 260px;
    max-width: 320px;
    width: 100%;
    margin: 0 auto;
    border: none;
    box-shadow: 0 4px 24px 0 rgba(0,0,0,0.10), 0 1.5px 8px rgba(40,116,240,0.06);
    transition: box-shadow 0.3s, border-color 0.3s, background 0.3s, color 0.3s;
    color: #222;
}



.product-card:hover .product-title,
.product-card:hover .product-category,
.product-card:hover .current-price,
.product-card:hover .original-price,
.product-card:hover .product-rating,
.product-card:hover .discount-badge,
.product-card:hover .wishlist-icon {
    color: #f5f5f5 !important;
}

.product-card:hover .discount-badge {
    background: #333b4a;
}

.product-card:hover .wishlist-icon {
    background: #232733;
}

.product-card:hover .add-to-cart {
    background: #232733;
    color: #fff;
}

.product-card:hover .buy-now {
    background: #2874f0;
    color: #fff;
}

.product-card .card-top {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 14px 16px 0 16px;
    position: absolute;
    width: 100%;
    z-index: 2;
}

.product-card .discount-badge {
    background: #111;
    color: #fff;
    font-size: 0.85rem;
    font-weight: 600;
    border-radius: 10px;
    padding: 4px 12px;
    margin-top: 0;
    box-shadow: 0 2px 8px rgba(40,116,240,0.10);
    transition: background 0.2s;
}

.product-card .discount-badge:hover {
    background: #2874f0;
}

.product-card .wishlist-icon {
    background: #fff;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(40,116,240,0.10);
    font-size: 1.3rem;
    color: #888;
    cursor: pointer;
    margin-top: 0;
    transition: color 0.2s, box-shadow 0.2s;
}

.product-card .wishlist-icon:hover {
    color: #2874f0;
    box-shadow: 0 4px 16px rgba(40,116,240,0.18);
}

.product-card .image-bg {
    background: linear-gradient(135deg, #ffe9b3 0%, #fff 100%);
    min-height: 140px;
    height: 140px;
    display: flex;
    align-items: flex-end;
    justify-content: flex-start;
    padding: 0;
    position: relative;
    width: 100%;
    overflow: hidden;
    border-bottom: none;
    box-shadow: 0 2px 12px 0 rgba(0,0,0,0.07);
}

.product-card .product-image {
    width: 100%;
    height: 240px;
    object-fit: contain;
    margin: 0;
    background: transparent;
    display: block;
}

.product-card .product-image:hover {
    transform: scale(1.06);
    border-color: rgba(0, 0, 0, 0);
}

.product-card .card-bottom {
    background: #fff;
    padding: 14px 16px 12px 16px;
    display: flex;
    flex-direction: column;
    gap: 6px;
    border-radius: 0 0 16px 16px;
    margin-top: -8px;
    border: none;
    box-shadow: 0 2px 12px 0 rgba(0,0,0,0.07);
}

.product-card .product-title {
    font-size: 1.1rem;
    font-weight: 700;
    color: #222;
    margin-bottom: 2px;
    margin-top: 0;
}

.product-card .product-category {
    font-size: 0.85rem;
    color: #888;
    font-weight: 400;
    margin-bottom: 0.75rem;
}

.product-card .product-price-row {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 4px;
}

.product-card .current-price {
    font-size: 1.15rem;
    font-weight: 700;
    color: #111;
}

.product-card .original-price {
    font-size: 1rem;
    color: #aaa;
    text-decoration: line-through;
    font-weight: 400;
}

.product-card .product-rating {
    color: #e6b800;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 2px;
    margin-bottom: 0;
}

.product-card .product-action-buttons {
    display: flex;
    flex-direction: row;
    gap: 0;
    width: 100%;
    margin-top: 10px;
}

.product-card .add-to-cart, .product-card .buy-now {
    flex: 1 1 0;
    min-width: 0;
    padding: 0.85rem 0;
    border-radius: 0 0 0 12px;
    font-weight: 600;
    font-size: 1rem;
    border: none;
    cursor: pointer;
    transition: all 0.3s;
    text-align: center;
    height: 44px;
    box-shadow: none;
    margin: 0;
}

.product-card .add-to-cart {
    background: #111;
    color: #fff;
    border-radius: 0 0 0 12px;
    border-right: 1.5px solid #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.product-card .add-to-cart:hover {
    background: #222;
}

.product-card .buy-now {
    background: #2563eb;
    color: #fff;
    border-radius: 0 0 12px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.product-card .buy-now:hover {
    background: #1741a6;
}

.product-card .view-details {
    background: #111;
    color: #fff;
    border-radius: 0 0 12px 0;
    border-left: 1.5px solid #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    flex: 1 1 0;
    min-width: 0;
    padding: 0.85rem 0;
    font-weight: 600;
    font-size: 1rem;
    border: none;
    cursor: pointer;
    transition: all 0.3s;
    text-align: center;
    height: 44px;
    box-shadow: none;
    margin: 0;
    text-decoration: none;
}

.product-card .view-details:hover {
    background: #222;
    color: #fff;
    text-decoration: none;
}



/* Featured Products Grid */
.featured-products {
    padding: 8rem 0;
    background: #f8f8f8;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 3rem;
}

.featured-card {
    background: white;
    padding: 2.5rem;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.08);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    border: 1px solid #e5e5e5;
}

.featured-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 30px 80px rgba(0, 0, 0, 0.12);
}

.new-badge {
    position: absolute;
    top: 2rem;
    right: 2rem;
    background: #000;
    color: white;
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.rating {
    display: flex;
    gap: 0.3rem;
    margin: 1rem 0;
}

.star {
    color: #000;
    font-size: 1.1rem;
}

/* Why Choose Us */
.why-choose-us {
    padding: 8rem 0;
    background: #000;
    color: white;
}

.why-choose-us .section-title {
    color: white;
}

.why-choose-us .section-title::after {
    background: white;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 4rem;
}

.feature-card {
    text-align: center;
    padding: 3rem;
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.feature-card:hover {
    transform: translateY(-5px);
}

.feature-icon {
    font-size: 4rem;
    color: white;
    margin-bottom: 2rem;
}

.feature-card h3 {
    font-family: 'Playfair Display', serif;
    font-size: 1.6rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
}

.feature-card p {
    font-size: 1.1rem;
    line-height: 1.6;
    opacity: 0.9;
}

/* Testimonials */
.testimonials {
    padding: 8rem 0;
    background: #f8f8f8;
}

.testimonials-slider {
    display: flex;
    gap: 3rem;
    overflow-x: auto;
    padding-bottom: 2rem;
    scroll-behavior: smooth;
}

.testimonials-slider::-webkit-scrollbar {
    height: 8px;
}

.testimonials-slider::-webkit-scrollbar-track {
    background: #e5e5e5;
}

.testimonials-slider::-webkit-scrollbar-thumb {
    background: #000;
}

.testimonial-card {
    min-width: 400px;
    background: white;
    padding: 3rem;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.08);
    border: 1px solid #e5e5e5;
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.testimonial-card:hover {
    transform: translateY(-5px);
}

.testimonial-text {
    font-style: italic;
    margin-bottom: 2rem;
    line-height: 1.7;
    font-size: 1.1rem;
    color: #333;
}

.testimonial-author {
    font-family: 'Playfair Display', serif;
    font-weight: 700;
    font-size: 1.2rem;
    color: #000;
}

.testimonial-role {
    font-size: 1rem;
    color: #666;
    margin-top: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Certifications */
.certifications {
    padding: 4rem 0;
    background: white;
}

.cert-grid {
    display: flex;
    justify-content: center;
    gap: 5rem;
    flex-wrap: wrap;
}

.cert-item {
    text-align: center;
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.cert-item:hover {
    transform: scale(1.1);
}

.cert-icon {
    font-size: 3rem;
    color: #000;
    margin-bottom: 1rem;
}

.cert-item div:last-child {
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Newsletter */
.newsletter {
    background: #000;
    color: white;
    padding: 5rem 0;
    text-align: center;
}

.newsletter h2 {
    font-family: 'Playfair Display', serif;
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
}

.newsletter p {
    font-size: 1.2rem;
    margin-bottom: 3rem;
    opacity: 0.9;
}

.newsletter-form {
    display: flex;
    max-width: 500px;
    margin: 0 auto;
    gap: 0;
}

.newsletter-input {
    flex: 1;
    padding: 1.2rem 1.5rem;
    border: 2px solid white;
    outline: none;
    font-size: 1rem;
    background: transparent;
    color: white;
}

.newsletter-input::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.newsletter-btn {
    background: white;
    color: #000;
    border: 2px solid white;
    padding: 1.2rem 2rem;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.newsletter-btn:hover {
    background: transparent;
    color: white;
    transform: translateX(-5px);
}

/* Footer */
.footer {
    background: #111;
    color: white;
    padding: 5rem 0 2rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 3rem;
    margin-bottom: 3rem;
}

.footer-section h3 {
    font-family: 'Playfair Display', serif;
    margin-bottom: 2rem;
    color: white;
    font-size: 1.4rem;
    font-weight: 700;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 1rem;
}

.footer-section ul li a {
    color: #ccc;
    text-decoration: none;
    transition: all 0.3s ease;
    font-weight: 500;
}

.footer-section ul li a:hover {
    color: white;
    transform: translateX(5px);
}

.footer-section p {
    color: #ccc;
    line-height: 1.6;
    margin-bottom: 2rem;
}

.social-links {
    display: flex;
    gap: 1.5rem;
}

.social-links a {
    color: #ccc;
    font-size: 1.8rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.social-links a:hover {
    color: white;
    transform: translateY(-3px) scale(1.2);
}

.footer-bottom {
    border-top: 1px solid #333;
    padding-top: 2rem;
    text-align: center;
    color: #999;
    font-weight: 500;
}

/* Animations */
@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-on-scroll, .animate-on-scroll.animated {
    opacity: 1 !important;
    transform: none !important;
    transition: none !important;
}

/* Premium Loading Animation */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #000;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity 0.5s ease;
}

.loading-overlay.hidden {
    opacity: 0;
    pointer-events: none;
}

.loader {
    width: 50px;
    height: 50px;
    border: 2px solid #333;
    border-top: 2px solid #fff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 20px;
    }

    .navbar {
        flex-direction: column;
        gap: 1.5rem;
    }

    .search-container {
        margin: 0;
        max-width: 100%;
    }

    .nav-links {
        gap: 1.5rem;
        justify-content: center;
    }

    .hero {
        padding: 5rem 0;
    }

    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 3rem;
    }

    .hero-text h1 {
        font-size: 3rem;
    }

    .hero-buttons {
        justify-content: center;
        flex-direction: column;
        align-items: center;
    }

    .section-title {
        font-size: 2.5rem;
    }

    .newsletter-form {
        flex-direction: column;
        gap: 1rem;
    }



    .cert-grid {
        gap: 3rem;
    }

    .products-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
    }

    .product-image-container {
        height: 200px;
        padding: 0.8rem;
    }

    .product-image {
        max-width: 85%;
        max-height: 85%;
    }
}

@media (max-width: 480px) {
    .hero-text h1 {
        font-size: 2.5rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .category-grid {
        grid-template-columns: 1fr;
    }

    .products-grid {
        grid-template-columns: 1fr;
    }

    .features-grid {
        grid-template-columns: 1fr;
    }

    .testimonials-slider {
        gap: 2rem;
    }

    .testimonial-card {
        min-width: 300px;
    }

    .product-card .product-image {
        height: 220px;
    }
}

/* Products Section Styles */
.products-section {
    padding: 4rem 0;
    background: #f8f8f8;
}

.products-section .section-title {
    margin-bottom: 3rem;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    padding: 2rem 0;
    align-items: stretch;
}

.product-card {
    background: white;
    padding: 1rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: none;
    display: flex;
    flex-direction: column;
    width: 140%;
    height: 100%;
}

.product-image-container {
    width: 100%;
    height: 280px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-radius: 8px;
    margin-bottom: 1rem;
    overflow: hidden;
    border: none;
    padding: 1.2rem;
}

.product-image {
    max-width: 95%;
    max-height: 95%;
    object-fit: contain;
    padding: 0;
    transition: all 0.3s ease;
    filter: drop-shadow(0 2px 8px rgba(0,0,0,0.1));
}

.product-card:hover .product-image {
    transform: scale(1.05);
 
}

.placeholder-image {
    color: #666;
    font-size: 0.9rem;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    width: 100%;
}

.product-info {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.product-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.25rem;
    line-height: 1.3;
}

.product-description {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 1rem;
    line-height: 1.5;
    display: block; /* Show description */
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
}

/* New styles for variant selector */
.variant-selector {
    margin: 1.5rem 0 1rem 0;
    width: 100%;
}

.weight-options {
    display: flex;
    flex-direction: row;
    gap: 1.2rem;
    flex-wrap: nowrap;
    justify-content: flex-start;
    align-items: center;
    background: #fafbfc;
    border-radius: 16px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.06);
    padding: 18px 16px;
    margin-bottom: 1rem;
    border: 2px solid #333;
}

.variant-option {
    width: 64px;
    height: 64px;
    min-width: 64px;
    min-height: 64px;
    max-width: 64px;
    max-height: 64px;
    font-size: 0.95rem;
    padding: 0;
    border: 2px solid #333;
    border-radius: 12px;
    background: #fff;
    cursor: pointer;
    font-weight: 600;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
    box-shadow: 0 1px 6px rgba(0,0,0,0.03);
    position: relative;
    margin: 0;
    text-align: center;
    text-decoration: none;
}
.variant-option.selected {
    border-color: #222;
    background: #222;
    color: #fff;
}
.variant-option:hover:not(.out-of-stock):not(:disabled) {
    border-color: #007bff;
    background: #f0f8ff;
    color: #222;
}
.variant-option.out-of-stock, .variant-option:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    background: #f8f8f8;
    border-color: #eee;
    color: #c00;
}
.variant-option .stock-status {
    font-size: 0.9em;
    color: #c00;
    margin-top: 0.25rem;
}

.product-actions {
    margin-top: auto;
    padding-top: 0.5rem;
}

.variant-selector {
    margin-bottom: 0.75rem;
}

.weight-options {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.variant-option {
    background: #f5f5f5;
    color: #333;
    border: 2px solid #333;
    border-radius: 4px;
    padding: 6px 12px;
    font-size: 0.85rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 40px;
    text-align: center;
    text-decoration: none;
}

.variant-option:hover {
    background: #e9ecef;
    border-color: #bbb;
}

.variant-option.selected {
    background: #333;
    color: white;
    border-color: #333;
}

.variant-option.out-of-stock {
    background: #f8f8f8;
    color: #999;
    cursor: not-allowed;
    opacity: 0.6;
}

.no-variants {
    font-size: 0.8rem;
    color: #888;
    margin-bottom: 0.75rem;
    font-style: italic;
}

.product-rating {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.product-action-buttons {
    display: flex;
    gap: 8px;
}

.add-to-cart, .buy-now {
    flex: 1;
    padding: 10px 16px;
    border: none;
    border-radius: 4px;
    font-weight: 600;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.add-to-cart {
    background: #333;
    color: white;
}

.add-to-cart:hover {
    background: #555;
}

.buy-now {
    background: #ff9800;
    color: white;
}

.buy-now:hover {
    background: #f57c00;
}

.product-price {
    display: flex;
    align-items: baseline;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.current-price {
    font-size: 1.25rem;
    font-weight: 700;
    color: #000;
}

.quantity-selector {
    display: flex;
    align-items: center;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-bottom: 1rem;
    width: 100%;
    max-width: 120px;
}

/* Filters and Weight Variants */
.filter-section {
    margin: 2rem 0;
    text-align: center;
}

.filter-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
    margin-bottom: 2rem;
}

.filter-btn {
    padding: 0.75rem 1.5rem;
    border: 2px solid #333;
    background: transparent;
    color: #333;
    font-weight: 600;
    border-radius: 30px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-btn:hover {
    background: #333;
    color: white;
}

.filter-btn.active {
    background: #333;
    color: white;
}

.variant-selector label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #666;
}

/* Category Selection Pills */
.category-selection {
    padding: 20px 0 40px;
}

.select-concern {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
    justify-content: flex-start; /* Move to left side */
}

.filter-icon {
    color: #666;
    font-size: 1.2rem;
}

.select-label {
    color: #666;
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
}

.category-pills {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    flex: 1;
    justify-content: flex-start; /* Move to left side */
}

.pill {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 16px;
    background: #fff;
    border: 2px solid #666;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    text-decoration: none;
    position: relative;
    overflow: hidden;
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    animation: pillFadeIn 0.6s ease-out forwards;
    opacity: 0;
}

/* Staggered animation for pills */
.pill:nth-child(1) { animation-delay: 0.1s; }
.pill:nth-child(2) { animation-delay: 0.2s; }
.pill:nth-child(3) { animation-delay: 0.3s; }
.pill:nth-child(4) { animation-delay: 0.4s; }
.pill:nth-child(5) { animation-delay: 0.5s; }
.pill:nth-child(6) { animation-delay: 0.6s; }

@keyframes pillFadeIn {
    from {
        opacity: 0;
        transform: translateY(20px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.pill::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.6s ease;
}

.pill:hover::before {
    left: 100%;
}

.pill:hover {
    border-color: #333;
    background: #f9f9f9;
    text-decoration: none;
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.pill:active {
    transform: translateY(-1px) scale(0.98);
    transition: all 0.1s ease;
}

.pill.active {
    background: #f5f9f5;
    border-color: #333;
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
    animation: pillPulse 2s ease-in-out infinite;
}

@keyframes pillPulse {
    0%, 100% {
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
    }
    50% {
        box-shadow: 0 8px 25px rgba(76, 175, 80, 0.2);
    }
}

.pill:focus {
    text-decoration: none;
    outline: none;
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.3);
}

.category-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: auto 0;
    transition: transform 0.3s ease;
    border: 2px solid rgba(255, 255, 255, 0.8);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.pill:hover .category-icon {
    transform: rotate(5deg) scale(1.1);
}

.pill.active .category-icon {
    transform: scale(1.05);
    border-color: #4CAF50;
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.pill span {
    color: #333;
    font-size: 0.95rem;
    white-space: nowrap;
    text-decoration: none;
    transition: all 0.3s ease;
    font-weight: 500;
}

.pill:hover span {
    color: #000;
    font-weight: 600;
}

.pill.active span {
    color: #2e7d32;
    font-weight: 600;
}

.check-icon {
    color: #4CAF50;
    font-weight: bold;
    animation: checkBounce 0.6s ease-out;
    transform-origin: center;
}

@keyframes checkBounce {
    0% {
        transform: scale(0);
        opacity: 0;
    }
    50% {
        transform: scale(1.3);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Enhanced category pills container with hover effects */
.category-pills:hover .pill:not(:hover) {
    opacity: 0.7;
    transform: scale(0.95);
}

/* Add a subtle breathing animation to the active pill */
.pill.active {
    animation: pillPulse 2s ease-in-out infinite, pillFadeIn 0.6s ease-out forwards;
}

@keyframes pillBreath {
    0%, 100% {
        transform: translateY(-2px) scale(1);
    }
    50% {
        transform: translateY(-4px) scale(1.01);
    }
}

/* Make the pills scrollable on mobile */
@media (max-width: 768px) {
    .category-pills {
        display: flex;
        overflow-x: auto;
        flex-wrap: nowrap;
        padding-bottom: 10px;
        -webkit-overflow-scrolling: touch;
        scrollbar-width: none; /* Firefox */
    }

    .category-pills::-webkit-scrollbar {
        display: none; /* Chrome, Safari, Edge */
    }

    .pill {
        flex-shrink: 0;
    }

    .select-concern {
        gap: 10px;
    }
}

/* Contact Page Styles */
.contact-hero {
    background: linear-gradient(135deg, #b1c8df 0%, #eb2929 100%);
    padding: 60px 0 0;
    position: relative;
    text-align: center;
    color: #fff;
    border-radius: 18px 18px 0 0;
}

.contact-hero .hero-text h1,
.contact-hero .hero-text .contact-heading {
    font-size: 2.8rem;
    font-weight: 800;
    color: #fff;
    margin-bottom: 0.7rem;
}

.contact-hero .hero-text p,
.contact-hero .hero-text .contact-description {
    color: #e0e0e0;
    font-size: 1.1rem;
    margin-bottom: 2.5rem;
}

.contact-form-section {
    margin-top: -60px;
    padding-bottom: 80px;
    position: relative;
    z-index: 2;
    background: none;
    display: flex;
    justify-content: flex-start; /* Move the contact box to the left */
}

.contact-form-container {
    max-width: 100000px;
    min-height:500px;
    width: 100%;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.contact-form-container.two-column {
    max-width: 1000px;
    min-height: 420px;
    width: 100%;
    margin-left: 0;
    margin-right: auto;
}

.contact-form {
    display: flex;
    flex-direction: column;
    gap: 1.2rem;
}

.contact-form-row {
    display: flex;
    gap: 1rem;
}

.contact-form-row .form-input {
    flex: 1;
}

.form-input {
    width: 100%;
    padding: 14px 16px;
    border: 1.5px solid #e0e0e0;
    border-radius: 7px;
    font-size: 1rem;
    background: #f8f8f8;
    transition: border 0.2s;
}
.form-input:focus {
    border: 1.5px solid #1e90ff;
    outline: none;
    background: #fff;
}
.form-textarea {
    min-height: 90px;
    resize: vertical;
}

.contact-form .form-btn {
    width: 100%;
    background: linear-gradient(90deg, #1e90ff 0%, #0a3d62 100%);
    color: #fff;
    border: none;
    border-radius: 7px;
    padding: 15px 0;
    font-size: 1.1rem;
    font-weight: 700;
    cursor: pointer;
    margin-top: 0.5rem;
    transition: background 0.2s, box-shadow 0.2s;
    box-shadow: 0 2px 8px rgba(30,144,255,0.08);
}
.contact-form .form-btn:hover {
    background: linear-gradient(90deg, #0a3d62 0%, #1e90ff 100%);
    box-shadow: 0 4px 16px rgba(30,144,255,0.13);
}

@media (max-width: 600px) {
    .contact-form-container {
        padding: 20px 8px 15px 8px;
    }
    .contact-form-row {
        flex-direction: column;
        gap: 0.7rem;
    }
}

@media (max-width: 900px) {
    .contact-form-section {
        justify-content: center;
    }
    .contact-form-container.two-column {
        width: 100%;
        max-width: 100%;
        min-height: 420px;
        flex-direction: column;
        margin: 0 auto;
    }
}

/* ===== COMPREHENSIVE MOBILE RESPONSIVE STYLES ===== */

/* Large Tablets */
@media (max-width: 1024px) {
    .container {
        padding: 0 25px;
    }

    .hero-text h1 {
        font-size: 3.2rem;
    }

    .section-title {
        font-size: 2.8rem;
    }

    .category-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
    }

    .features-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
    }

    .testimonials-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
    }
}

/* Cart Sidebar Styles */
.cart-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 9998;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.cart-overlay.active {
    opacity: 1;
    visibility: visible;
}

.cart-sidebar {
    position: fixed;
    top: 0;
    right: -550px;
    width: 550px;
    height: 100vh;
    min-height: 100vh;
    background: white;
    z-index: 9999;
    transition: right 0.3s ease;
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    overflow-y: auto;
}

.cart-sidebar.active {
    right: 0;
}

.cart-sidebar-header {
    padding: 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f8f9fa;
    position: sticky;
    top: 0;
    z-index: 10;
}

.cart-sidebar-header h2 {
    margin: 0;
    font-size: 1.5rem;
    color: #333;
}

.cart-close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #666;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.cart-close-btn:hover {
    background: #e9ecef;
    color: #333;
}

.cart-sidebar-content {
    flex: 1;
    overflow-y: auto;
    padding: 25px;
    min-height: calc(100vh - 120px);
    display: flex;
    flex-direction: column;
}

/* Kapiva Coins Banner */
.kapiva-coins-banner {
    background: linear-gradient(135deg, #ffa500, #ff8c00);
    color: white;
    padding: 12px 20px;
    margin: -20px -20px 20px -20px;
    text-align: center;
    font-weight: bold;
    font-size: 0.9rem;
}

.coins-icon {
    margin-right: 8px;
    font-size: 1.1rem;
}

.empty-cart-sidebar {
    text-align: center;
    padding: 40px 20px;
}

.empty-cart-sidebar .empty-cart-icon {
    font-size: 4rem;
    color: #ddd;
    margin-bottom: 20px;
}

.empty-cart-sidebar h3 {
    color: #666;
    margin-bottom: 10px;
}

.empty-cart-sidebar p {
    color: #999;
    margin-bottom: 20px;
}

.continue-shopping-btn {
    display: inline-block;
    padding: 12px 24px;
    background: #007bff;
    color: white;
    text-decoration: none;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.continue-shopping-btn:hover {
    background: #0056b3;
    transform: translateY(-2px);
}

.cart-sidebar-item {
    display: flex;
    gap: 15px;
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    background: #f8f9fa;
    margin-bottom: 15px;
    border-radius: 8px;
}

.cart-item-image {
    width: 100px;
    height: 100px;
    flex-shrink: 0;
}

.cart-item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 8px;
}

.cart-item-details {
    flex: 1;
}

.cart-item-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 5px;
}

.cart-item-details h4 {
    margin: 0;
    font-size: 1rem;
    color: #333;
    line-height: 1.3;
    font-weight: 600;
    flex: 1;
    padding-right: 10px;
}

.remove-item-btn {
    background: none;
    border: none;
    color: #999;
    cursor: pointer;
    padding: 2px;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.remove-item-btn:hover {
    background: #f5f5f5;
    color: #e74c3c;
    transform: scale(1.1);
}

.remove-item-btn i {
    font-size: 0.8rem;
}

.variant-name {
    font-size: 0.8rem;
    color: #666;
    margin: 0 0 8px 0;
}

.cart-item-price-row {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}

.quantity-price {
    font-size: 0.9rem;
    color: #333;
    font-weight: 500;
}

.original-price {
    font-size: 0.8rem;
    color: #999;
    text-decoration: line-through;
}

.cart-item-price {
    font-weight: bold;
    color: #007bff;
    margin-bottom: 10px;
}

.quantity-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.qty-btn {
    width: 30px;
    height: 30px;
    border: 1px solid #ddd;
    background: white;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    transition: all 0.3s ease;
}

.qty-btn:hover {
    background: #f8f9fa;
    border-color: #007bff;
}

.quantity-display {
    min-width: 30px;
    text-align: center;
    font-weight: bold;
}

.cart-item-actions {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 10px;
}

.item-total {
    font-weight: bold;
    color: #333;
    font-size: 1.1rem;
}

.remove-item-btn {
    background: none;
    border: none;
    color: #dc3545;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.remove-item-btn:hover {
    background: #f8d7da;
}

.cart-sidebar-summary {
    margin-top: 20px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
}

.cart-summary-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
}

.cart-summary-row:last-child {
    margin-bottom: 0;
}

.total-amount {
    font-weight: bold;
    font-size: 1.2rem;
    color: #333;
}

.kapiva-coins {
    color: #ffa500;
    font-weight: bold;
}

.cart-sidebar-actions {
    margin-top: 20px;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.checkout-btn {
    width: 100%;
    padding: 15px;
    background: #8BC34A;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.checkout-btn:hover {
    background: #7CB342;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(139, 195, 74, 0.3);
}

.view-cart-btn {
    width: 100%;
    padding: 12px;
    background: transparent;
    color: #007bff;
    border: 2px solid #007bff;
    border-radius: 8px;
    text-decoration: none;
    text-align: center;
    font-weight: bold;
    transition: all 0.3s ease;
}

.view-cart-btn:hover {
    background: #007bff;
    color: white;
}

/* Add For Better Results Section */
.add-for-better-results {
    background: #f0f4f0;
    padding: 25px;
    margin: 25px 0;
    border-radius: 8px;
}

.add-for-better-results h3 {
    margin: 0 0 5px 0;
    font-size: 1.2rem;
    color: #333;
    font-weight: 600;
}

.recommendation-subtitle {
    font-size: 0.9rem;
    color: #666;
    margin: 0 0 15px 0;
}

.recommended-products-grid {
    display: flex;
    gap: 15px;
    overflow-x: auto;
    padding-bottom: 10px;
}

.recommended-product {
    min-width: 160px;
    max-width: 160px;
    background: white;
    border-radius: 8px;
    padding: 12px;
    position: relative;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
}

.discount-badge {
    position: absolute;
    top: 8px;
    left: 8px;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.7rem;
    font-weight: 600;
    color: white;
    z-index: 1;
}

.discount-badge.green {
    background: #4CAF50;
}

.discount-badge.blue {
    background: #2196F3;
}

.discount-badge.orange {
    background: #FF9800;
}

.product-image {
    width: 100%;
    height: 80px;
    margin-bottom: 8px;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 4px;
}

.product-rating {
    margin-bottom: 5px;
}

.stars {
    font-size: 0.7rem;
    color: #666;
}

.recommended-product h4 {
    font-size: 0.8rem;
    margin: 0 0 5px 0;
    color: #333;
    line-height: 1.2;
}

.product-price {
    font-size: 0.9rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
}

.add-recommended-btn {
    width: 100%;
    padding: 8px 12px;
    background: #333;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 0.8rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: auto;
    text-transform: uppercase;
}

.add-recommended-btn:hover {
    background: #555;
}

/* Kapiva Coins Banner */
.kapiva-coins-banner {
    background: linear-gradient(135deg, #FFA726, #FF9800);
    color: white;
    padding: 18px 20px;
    margin-bottom: 15px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1rem;
    font-weight: 600;
    min-height: 50px;
}

.coins-icon {
    font-size: 1.2rem;
}



/* Mobile Responsive for Cart Sidebar */
@media (max-width: 768px) {
    .cart-sidebar {
        width: 100%;
        right: -100%;
    }
}

@media (max-width: 1024px) and (min-width: 769px) {
    .cart-sidebar {
        width: 480px;
        right: -480px;
    }
}

    .cart-sidebar-header {
        padding: 15px;
    }

    .cart-sidebar-content {
        padding: 15px;
    }

    .cart-sidebar-item {
        gap: 10px;
    }

    .cart-item-image {
        width: 80px;
        height: 80px;
    }

    .cart-item-details h4 {
        font-size: 0.9rem;
    }
}

/* Checkout Page Styles - Couture Candy Design */
.checkout-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
    min-height: 80vh;
    background: #f8f9fa;
}

.checkout-header {
    background: white;
    padding: 30px;
    border-radius: 12px;
    margin-bottom: 30px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.checkout-header h1 {
    text-align: center;
    margin-bottom: 30px;
    color: #333;
    font-size: 2.2rem;
    font-weight: 700;
}

.checkout-steps {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 40px;
    margin-bottom: 0;
}

.step {
    display: flex;
    align-items: center;
    gap: 12px;
    opacity: 0.5;
    transition: all 0.3s ease;
}

.step.active {
    opacity: 1;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #e9ecef;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.1rem;
    transition: all 0.3s ease;
}

.step.active .step-number {
    background: #333;
    color: white;
}

.step-text {
    font-weight: 600;
    color: #333;
    font-size: 1rem;
}

.checkout-layout {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
    margin-top: 0;
}

/* Checkout Main Section */
.checkout-main {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
}

.checkout-step-content {
    display: none;
    padding: 40px;
}

.checkout-step-content.active {
    display: block;
}

.checkout-step-content h2 {
    margin-bottom: 30px;
    color: #333;
    font-size: 1.8rem;
    font-weight: 600;
}

/* Step 1: Checkout Options */
.checkout-options {
    max-width: 500px;
    margin: 0 auto;
}

.express-checkout {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 30px;
}

.express-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    padding: 15px 20px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    background: white;
    color: #333;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
}

.express-btn:hover {
    border-color: #333;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.express-btn i {
    font-size: 1.2rem;
}

.amazon-pay {
    border-color: #ff9900;
    color: #ff9900;
}

.amazon-pay:hover {
    background: #ff9900;
    color: white;
}

.google-pay {
    border-color: #4285f4;
    color: #4285f4;
}

.google-pay:hover {
    background: #4285f4;
    color: white;
}

.paypal {
    border-color: #0070ba;
    color: #0070ba;
}

.paypal:hover {
    background: #0070ba;
    color: white;
}

.divider {
    text-align: center;
    margin: 25px 0;
    position: relative;
}

.divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e9ecef;
}

.divider span {
    background: white;
    padding: 0 20px;
    color: #6c757d;
    font-weight: 500;
}

.guest-checkout, .login-option {
    text-align: center;
}

.guest-btn, .login-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    width: 100%;
    padding: 15px 20px;
    border: 2px solid #333;
    border-radius: 8px;
    background: #333;
    color: white;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 10px;
}

.guest-btn:hover, .login-btn:hover {
    background: #555;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

.login-btn {
    background: transparent;
    color: #333;
}

.login-btn:hover {
    background: #333;
    color: white;
}

.guest-text, .login-text {
    color: #6c757d;
    font-size: 0.9rem;
    margin: 0;
}

/* Order Summary */
.order-summary {
    background: white;
    padding: 30px;
    border-radius: 12px;
    height: fit-content;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: sticky;
    top: 20px;
}

.order-summary h3 {
    margin-bottom: 25px;
    color: #333;
    font-size: 1.5rem;
    font-weight: 600;
    border-bottom: 2px solid #f8f9fa;
    padding-bottom: 15px;
}

.order-items {
    margin-bottom: 25px;
}

.order-item {
    display: flex;
    gap: 15px;
    padding: 15px 0;
    border-bottom: 1px solid #f8f9fa;
}

.order-item:last-child {
    border-bottom: none;
}

.item-image {
    position: relative;
    width: 60px;
    height: 60px;
    flex-shrink: 0;
}

.item-image img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    border-radius: 8px;
    border: 1px solid #f0f0f0;
}

.item-qty {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #333;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
}

.item-details {
    flex: 1;
}

.item-details h4 {
    margin: 0 0 5px 0;
    font-size: 1rem;
    color: #333;
    font-weight: 600;
}

.item-variant {
    margin: 0 0 8px 0;
    font-size: 0.85rem;
    color: #6c757d;
}

.item-price {
    font-weight: 700;
    color: #333;
    font-size: 1rem;
    margin: 0;
}

.order-totals {
    border-top: 2px solid #f8f9fa;
    padding-top: 20px;
    margin-top: 20px;
}

.total-line {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    font-size: 1rem;
}

.total-line:last-child {
    margin-bottom: 0;
}

.free-shipping {
    color: #28a745;
    font-weight: 600;
}

.final-total {
    font-size: 1.3rem;
    font-weight: 700;
    color: #333;
    border-top: 2px solid #f8f9fa;
    padding-top: 15px;
    margin-top: 15px;
}

.security-badges {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 25px;
    padding-top: 20px;
    border-top: 1px solid #f8f9fa;
}

.security-badge {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #28a745;
    font-size: 0.9rem;
    font-weight: 500;
}

.security-badge i {
    font-size: 1rem;
}

/* Form Styles */
.form-group {
    margin-bottom: 25px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.form-row.three-col {
    grid-template-columns: 1fr 1fr 1fr;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
    font-size: 0.95rem;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 15px;
    border: 2px solid #333;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #f8f9fa;
    text-decoration: none;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #333;
    background: white;
    box-shadow: 0 0 0 3px rgba(51, 51, 51, 0.1);
}

.form-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 40px;
    padding-top: 30px;
    border-top: 2px solid #f8f9fa;
}

.btn-back {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    background: transparent;
    color: #6c757d;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-back:hover {
    background: #f8f9fa;
    border-color: #333;
    color: #333;
}

.btn-continue, .btn-place-order {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 15px 30px;
    background: #333;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-continue:hover, .btn-place-order:hover {
    background: #555;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

.btn-place-order {
    background: #28a745;
    font-size: 1.2rem;
    padding: 18px 35px;
}

.btn-place-order:hover {
    background: #218838;
}

/* Payment Methods */
.payment-methods {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 30px;
}

.payment-method {
    display: block;
    cursor: pointer;
}

.payment-method input[type="radio"] {
    display: none;
}

.payment-method-content {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    background: #f8f9fa;
    transition: all 0.3s ease;
}

.payment-method input[type="radio"]:checked + .payment-method-content {
    border-color: #333;
    background: white;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.payment-method-content i {
    font-size: 1.5rem;
    color: #333;
    width: 30px;
    text-align: center;
}

.payment-method-content div {
    flex: 1;
}

.payment-method-content strong {
    display: block;
    font-size: 1.1rem;
    color: #333;
    margin-bottom: 4px;
}

.payment-method-content p {
    margin: 0;
    color: #6c757d;
    font-size: 0.9rem;
}

.place-order-btn {
    width: 100%;
    padding: 15px;
    background: #333;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.place-order-btn:hover {
    background: #555;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

/* Enhanced Mobile Styles for Better UX */
@media (max-width: 768px) {
    /* Header Enhancements */
    .header-icons {
        gap: 1rem;
    }

    .header-icon {
        font-size: 1.1rem;
        padding: 8px;
        border-radius: 50%;
        transition: all 0.3s ease;
    }

    .header-icon:hover {
        background: rgba(0, 0, 0, 0.1);
    }

    .cart-count {
        width: 18px;
        height: 18px;
        font-size: 0.7rem;
        top: -8px;
        right: -8px;
    }

    /* Navigation */
    .nav-links {
        flex-wrap: wrap;
        gap: 0.8rem;
        padding: 10px;
        justify-content: center;
    }

    .nav-links a {
        font-size: 0.85rem;
        padding: 8px 12px;
        border-radius: 4px;
        background: rgba(255, 255, 255, 0.1);
        transition: all 0.3s ease;
        white-space: nowrap;
    }

    .nav-links a:hover {
        background: rgba(255, 255, 255, 0.2);
        transform: translateY(-1px);
    }

    /* Hero Section */
    .hero-text p {
        font-size: 1rem;
        line-height: 1.6;
        margin-bottom: 2rem;
    }

    .hero-cta {
        padding: 12px 24px;
        font-size: 1rem;
        border-radius: 6px;
        width: auto;
        min-width: 200px;
    }

    /* Category Cards */
    .category-card {
        padding: 2rem 1.5rem;
        text-align: center;
        margin-bottom: 1rem;
    }

    .category-card h3 {
        font-size: 1.3rem;
        margin-bottom: 1rem;
    }

    .category-card p {
        font-size: 0.95rem;
        line-height: 1.5;
    }

    /* Feature Cards */
    .feature-card {
        padding: 2rem 1.5rem;
        text-align: center;
    }

    .feature-icon {
        font-size: 3rem;
        margin-bottom: 1.5rem;
    }

    .feature-card h3 {
        font-size: 1.3rem;
        margin-bottom: 1rem;
    }

    .feature-card p {
        font-size: 0.95rem;
        line-height: 1.6;
    }

    /* Footer */
    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .footer-section {
        margin-bottom: 1.5rem;
    }

    .footer-section h3 {
        font-size: 1.2rem;
        margin-bottom: 1rem;
    }

    .footer-section ul li {
        margin-bottom: 0.5rem;
    }

    .footer-section ul li a {
        font-size: 0.9rem;
    }

    .social-links {
        justify-content: center;
        gap: 1rem;
    }

    .social-links a {
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
    }

    /* Checkout Mobile Styles */
    .checkout-container {
        padding: 15px;
        background: white;
    }

    .checkout-header {
        padding: 20px;
        margin-bottom: 20px;
    }

    .checkout-header h1 {
        font-size: 1.8rem;
        margin-bottom: 20px;
    }

    .checkout-steps {
        gap: 20px;
        flex-wrap: wrap;
    }

    .step-text {
        display: none;
    }

    .checkout-layout {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .checkout-step-content {
        padding: 25px;
    }

    .checkout-step-content h2 {
        font-size: 1.5rem;
        margin-bottom: 25px;
    }

    .checkout-options {
        max-width: none;
    }

    .express-checkout {
        gap: 12px;
    }

    .express-btn {
        padding: 12px 16px;
        font-size: 0.95rem;
    }

    .order-summary {
        padding: 20px;
        position: static;
    }

    .order-summary h3 {
        font-size: 1.3rem;
        margin-bottom: 20px;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .form-row.three-col {
        grid-template-columns: 1fr;
    }

    .form-actions {
        flex-direction: column;
        gap: 15px;
        margin-top: 30px;
        padding-top: 20px;
    }

    .btn-back, .btn-continue, .btn-place-order {
        width: 100%;
        justify-content: center;
    }

    .payment-method-content {
        padding: 15px;
        gap: 12px;
    }

    .security-badges {
        flex-direction: column;
        gap: 10px;
        align-items: center;
    }
}

/* Authentication Pages Styles */
.auth-container {
    min-height: 80vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    background: #f8f9fa;
}

.auth-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    padding: 40px;
    width: 100%;
    max-width: 450px;
}

.auth-header {
    text-align: center;
    margin-bottom: 30px;
}

.auth-header h1 {
    font-size: 2rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 8px;
}

.auth-header p {
    color: #6c757d;
    font-size: 1rem;
    margin: 0;
}

.alert {
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 0.95rem;
}

.alert-error {
    background: #fff5f5;
    color: #e53e3e;
    border: 1px solid #fed7d7;
}

.alert-success {
    background: #f0fff4;
    color: #38a169;
    border: 1px solid #c6f6d5;
}

.auth-form {
    margin-bottom: 25px;
}

.password-input {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: color 0.3s ease;
}

.password-toggle:hover {
    color: #333;
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    font-size: 0.9rem;
    color: #333;
}

.checkbox-label input[type="checkbox"] {
    width: auto;
    margin: 0;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid #e9ecef;
    border-radius: 4px;
    position: relative;
    transition: all 0.3s ease;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: #333;
    border-color: #333;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: -2px;
    left: 2px;
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.forgot-link {
    color: #333;
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.forgot-link:hover {
    color: #555;
    text-decoration: underline;
}

.auth-btn {
    width: 100%;
    padding: 15px;
    background: #333;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.auth-btn:hover {
    background: #555;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

.auth-divider {
    text-align: center;
    margin: 25px 0;
    position: relative;
}

.auth-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e9ecef;
}

.auth-divider span {
    background: white;
    padding: 0 20px;
    color: #6c757d;
    font-weight: 500;
}

.social-auth {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 25px;
}

.social-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    padding: 12px 20px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    background: white;
    color: #333;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
}

.social-btn:hover {
    border-color: #333;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.google-btn {
    border-color: #db4437;
    color: #db4437;
}

.google-btn:hover {
    background: #db4437;
    color: white;
}

.facebook-btn {
    border-color: #4267b2;
    color: #4267b2;
}

.facebook-btn:hover {
    background: #4267b2;
    color: white;
}

.auth-footer {
    text-align: center;
}

.auth-footer p {
    color: #6c757d;
    font-size: 0.95rem;
    margin: 0;
}

.auth-footer a {
    color: #333;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.auth-footer a:hover {
    color: #555;
    text-decoration: underline;
}

.guest-checkout-option {
    margin-top: 30px;
}

.guest-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    padding: 25px;
    text-align: center;
}

.guest-card h3 {
    font-size: 1.3rem;
    color: #333;
    margin-bottom: 8px;
}

.guest-card p {
    color: #6c757d;
    margin-bottom: 20px;
}

.guest-btn {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    padding: 12px 24px;
    background: #28a745;
    color: white;
    text-decoration: none;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.guest-btn:hover {
    background: #218838;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.form-help {
    font-size: 0.8rem;
    color: #6c757d;
    margin-top: 4px;
}

/* Mobile Responsive for Auth */
@media (max-width: 768px) {
    .auth-container {
        padding: 20px 15px;
    }

    .auth-card {
        padding: 30px 25px;
    }

    .auth-header h1 {
        font-size: 1.7rem;
    }

    .form-options {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .social-auth {
        gap: 10px;
    }

    .social-btn {
        padding: 10px 16px;
        font-size: 0.95rem;
    }
}

/* Checkout User Integration Styles */
.user-greeting {
    color: #6c757d;
    font-size: 1rem;
    margin-bottom: 25px;
}

.user-checkout {
    text-align: center;
    margin: 25px 0;
}

.user-btn {
    display: inline-flex;
    align-items: center;
    gap: 12px;
    padding: 15px 30px;
    background: #28a745;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
}

.user-btn:hover {
    background: #218838;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.user-text {
    margin: 10px 0 0 0;
    color: #6c757d;
    font-size: 0.9rem;
}

.logout-option {
    text-align: center;
    margin: 20px 0;
}

.logout-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    background: #6c757d;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
}

.logout-btn:hover {
    background: #5a6268;
    color: white;
}

.saved-addresses {
    margin-bottom: 30px;
    padding: 25px;
    background: #f8f9fa;
    border-radius: 12px;
}

.saved-addresses h3 {
    margin: 0 0 20px 0;
    color: #333;
    font-size: 1.3rem;
    font-weight: 600;
}

.address-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 15px;
}

.address-option {
    cursor: pointer;
}

.address-option input[type="radio"] {
    display: none;
}

.address-card {
    padding: 20px;
    border: 2px solid #333;
    border-radius: 8px;
    background: white;
    transition: all 0.3s ease;
    position: relative;
    text-decoration: none;
}

.address-option input[type="radio"]:checked + .address-card {
    border-color: #28a745;
    background: #f8fff9;
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.2);
}

.address-card:hover {
    border-color: #333;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.address-card h4 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 1.1rem;
    font-weight: 600;
}

.address-card p {
    margin: 0 0 5px 0;
    color: #333;
    line-height: 1.4;
}

.address-card.new-address {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    min-height: 120px;
    border-style: dashed;
    color: #6c757d;
}

.address-card.new-address i {
    font-size: 2rem;
    margin-bottom: 10px;
    color: #28a745;
}

.address-card.new-address:hover {
    border-color: #28a745;
    background: #f8fff9;
}

.default-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: #28a745;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
}

/* Mobile responsive for checkout integration */
@media (max-width: 768px) {
    .address-options {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .saved-addresses {
        padding: 20px;
        margin-bottom: 25px;
    }

    .address-card {
        padding: 15px;
    }

    .user-btn {
        padding: 12px 24px;
        font-size: 1rem;
    }

    /* Modal responsive styles */
    .modal-content {
        width: 95%;
        margin: 5% auto;
        max-height: 85vh;
    }

    .modal-header,
    .modal-body {
        padding: 15px;
    }

    .summary-row,
    .details-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .item-row {
        grid-template-columns: 1fr;
        gap: 10px;
        text-align: left;
    }

    .item-quantity,
    .item-price,
    .item-total {
        text-align: left;
    }
}

/* Order Details Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: white;
    margin: 2% auto;
    padding: 0;
    border-radius: 12px;
    width: 80%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 40px rgba(0,0,0,0.3);
    animation: modalSlideIn 0.3s ease-out;
}

.large-modal {
    max-width: 900px;
    width: 90%;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid #e0e0e0;
    background: #f8f9fa;
    border-radius: 12px 12px 0 0;
}

.modal-header h3 {
    margin: 0;
    color: #333;
    font-size: 1.3rem;
    font-weight: 600;
}

.close {
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    line-height: 1;
    padding: 0;
    background: none;
    border: none;
}

.close:hover,
.close:focus {
    color: #333;
}

.modal-body {
    padding: 25px;
}

.loading-spinner {
    text-align: center;
    padding: 40px;
    color: #6c757d;
}

.loading-spinner i {
    font-size: 2rem;
    margin-bottom: 10px;
}

.error-message {
    text-align: center;
    padding: 40px;
    color: #dc3545;
    background: #f8d7da;
    border-radius: 8px;
    margin: 20px 0;
}

.order-details-container {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.order-summary,
.shipping-details,
.customer-details,
.order-items,
.payment-info,
.order-notes {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 20px;
}

.order-summary {
    background: #f8f9fa;
}

.summary-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.summary-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.summary-item label {
    font-weight: 600;
    color: #6c757d;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.summary-item span {
    color: #333;
    font-size: 1rem;
}

.order-number {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    color: #007bff;
}

.shipping-details h4,
.customer-details h4,
.order-items h4,
.payment-info h4,
.order-notes h4 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 1.1rem;
    font-weight: 600;
    border-bottom: 1px solid #e0e0e0;
    padding-bottom: 10px;
}

.details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.detail-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.detail-item.full-width {
    grid-column: 1 / -1;
}

.detail-item label {
    font-weight: 600;
    color: #6c757d;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.detail-item span {
    color: #333;
    font-size: 0.95rem;
}

.items-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.item-row {
    display: grid;
    grid-template-columns: 2fr auto auto auto;
    gap: 15px;
    align-items: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e0e0e0;
}

.item-info strong {
    color: #333;
    font-size: 0.95rem;
}

.item-info small {
    color: #6c757d;
    font-size: 0.8rem;
}

.item-quantity,
.item-price,
.item-total {
    text-align: right;
    font-weight: 600;
    color: #333;
}

.order-totals {
    background: #f8f9fa;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 20px;
}

.total-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e0e0e0;
}

.total-row:last-child {
    border-bottom: none;
}

.total-row.final-total {
    font-weight: bold;
    font-size: 1.1rem;
    color: #333;
    border-top: 2px solid #333;
    margin-top: 10px;
    padding-top: 15px;
}

.payment-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.payment-badge.pending {
    background: #f3f4f6;
    color: #374151;
}

.payment-badge.paid {
    background: #d4edda;
    color: #155724;
}

.payment-badge.failed {
    background: #f8d7da;
    color: #721c24;
}

.order-notes p {
    margin: 0;
    color: #333;
    line-height: 1.6;
}

/* Account Dashboard Styles */
.account-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 40px 20px;
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 40px;
    min-height: 80vh;
}

.account-sidebar {
    background: white;
    border-radius: 12px;
    padding: 30px;
    height: fit-content;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    position: sticky;
    top: 20px;
}

.user-profile {
    text-align: center;
    margin-bottom: 30px;
    padding-bottom: 25px;
    border-bottom: 2px solid #f8f9fa;
}

.user-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: #333;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    margin: 0 auto 15px;
}

.user-profile h3 {
    margin: 0 0 5px 0;
    color: #333;
    font-size: 1.3rem;
    font-weight: 600;
}

.user-profile p {
    margin: 0;
    color: #6c757d;
    font-size: 0.9rem;
}

.account-nav {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 15px 20px;
    color: #333;
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-weight: 500;
}

.nav-item:hover {
    background: #f8f9fa;
    color: #333;
    transform: translateX(5px);
}

.nav-item.active {
    background: #333;
    color: white;
}

.nav-item.logout {
    margin-top: 20px;
    color: #dc3545;
    border-top: 1px solid #f8f9fa;
    padding-top: 20px;
}

.nav-item.logout:hover {
    background: #dc3545;
    color: white;
}

.account-content {
    background: white;
    border-radius: 12px;
    padding: 40px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.account-section {
    display: none;
}

.account-section.active {
    display: block;
}

.account-section h2 {
    margin: 0 0 30px 0;
    color: #333;
    font-size: 2rem;
    font-weight: 700;
}

.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 25px;
    margin-bottom: 40px;
}

.stat-card {
    background: #f8f9fa;
    padding: 25px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    gap: 20px;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: #333;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.stat-info h3 {
    margin: 0 0 5px 0;
    font-size: 2rem;
    font-weight: 700;
    color: #333;
}

.stat-info p {
    margin: 0;
    color: #6c757d;
    font-size: 0.9rem;
}

.recent-orders h3 {
    margin: 0 0 20px 0;
    color: #333;
    font-size: 1.5rem;
    font-weight: 600;
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 20px;
    color: #ddd;
}

.empty-state p {
    font-size: 1.1rem;
    margin-bottom: 25px;
}

.orders-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.order-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.order-item:hover {
    background: #e9ecef;
}

.order-info h4 {
    margin: 0 0 5px 0;
    color: #333;
    font-size: 1.1rem;
    font-weight: 600;
}

.order-info p {
    margin: 0 0 8px 0;
    color: #333;
    font-weight: 600;
}

.order-status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-pending {
    background: #f3f4f6;
    color: #374151;
}

.status-confirmed {
    background: #d4edda;
    color: #155724;
}

.status-processing {
    background: #cce5ff;
    color: #004085;
}

.status-shipped {
    background: #e2e3e5;
    color: #383d41;
}

.status-delivered {
    background: #d1ecf1;
    color: #0c5460;
}

.status-cancelled {
    background: #f8d7da;
    color: #721c24;
}

.order-date {
    color: #6c757d;
    font-size: 0.9rem;
}

.profile-form {
    max-width: 600px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.form-group {
    margin-bottom: 25px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
    font-size: 0.95rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 15px;
    border: 2px solid #333;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    text-decoration: none;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #000;
    box-shadow: 0 0 0 3px rgba(51, 51, 51, 0.1);
    text-decoration: none;
}

.form-group select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23333' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 12px center;
    background-repeat: no-repeat;
    background-size: 16px;
    padding-right: 40px;
    text-decoration: none;
}

.form-group input:disabled {
    background: #f8f9fa;
    color: #6c757d;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
}

.btn-primary {
    background: #333;
    color: white;
}

.btn-primary:hover {
    background: #555;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

.btn-sm {
    padding: 8px 16px;
    font-size: 0.9rem;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
}

.orders-table {
    overflow-x: auto;
}

.orders-table table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

.orders-table th,
.orders-table td {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid #e9ecef;
}

.orders-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #333;
}

.addresses-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
}

.address-card {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 25px;
    transition: all 0.3s ease;
}

.address-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.address-card.add-new {
    border: 2px dashed #ddd;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    background: white;
}

.address-card.add-new:hover {
    border-color: #333;
    background: #f8f9fa;
}

.add-address-content {
    text-align: center;
    color: #6c757d;
}

.add-address-content i {
    font-size: 2rem;
    margin-bottom: 15px;
}

.add-address-content h4 {
    margin: 0 0 8px 0;
    color: #333;
}

.add-address-content p {
    margin: 0;
    font-size: 0.9rem;
}

.address-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.address-header h4 {
    margin: 0;
    color: #333;
    font-size: 1.1rem;
    font-weight: 600;
}

.default-badge {
    background: #28a745;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
}

.address-details p {
    margin: 0 0 5px 0;
    color: #333;
    line-height: 1.4;
}

.address-actions {
    margin-top: 20px;
    display: flex;
    gap: 10px;
}

.preferences-form {
    max-width: 600px;
}

.preference-group {
    margin-bottom: 40px;
}

.preference-group h3 {
    margin: 0 0 20px 0;
    color: #333;
    font-size: 1.3rem;
    font-weight: 600;
}

.preference-item {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 15px;
}

.switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 34px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #333;
}

input:checked + .slider:before {
    transform: translateX(26px);
}

.preference-info h4 {
    margin: 0 0 5px 0;
    color: #333;
    font-size: 1.1rem;
    font-weight: 600;
}

.preference-info p {
    margin: 0;
    color: #6c757d;
    font-size: 0.9rem;
}

/* Mobile Responsive for Account */
@media (max-width: 768px) {
    .account-container {
        grid-template-columns: 1fr;
        gap: 20px;
        padding: 20px 15px;
    }

    .account-sidebar {
        position: static;
        padding: 20px;
    }

    .account-content {
        padding: 25px 20px;
    }

    .dashboard-stats {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .addresses-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .orders-table {
        font-size: 0.9rem;
    }

    .orders-table th,
    .orders-table td {
        padding: 10px 8px;
    }
}